package message_forward

import (
	"encoding/json"
	"fmt"
	"time"

	"zeka-go/internal/services/logger"
	"zeka-go/internal/types"
)

// MessageForwardTask 消息转发队列任务结构（简化版）
type MessageForwardTask struct {
	ID              string               `json:"id"`                 // 任务唯一标识
	OriginalMessage string               `json:"original_message"`   // 原始消息内容
	TargetChannels  []string             `json:"target_channels"`    // 目标频道ID列表
	SourceChannel   string               `json:"source_channel"`     // 源频道ID
	AuthorID        string               `json:"author_id"`          // 消息作者ID
	AuthorName      string               `json:"author_name"`        // 消息作者显示名称
	GuildID         string               `json:"guild_id"`           // 服务器ID
	MessageID       string               `json:"message_id"`         // 原始消息ID
	CreatedAt       time.Time            `json:"created_at"`         // 任务创建时间
	MappingName     string               `json:"mapping_name"`       // 映射配置名称
	Products        []*types.ProductItem `json:"products,omitempty"` // 产品信息

	// 运行时字段（不序列化）
	ProcessedMessage string `json:"-"` // 处理后的消息内容
}

// Validate 验证任务数据
func (t *MessageForwardTask) Validate() error {
	if t.ID == "" {
		return types.NewValidationError("id", t.ID, "任务ID不能为空")
	}

	// 注意：原始消息可以为空，这在处理嵌入消息、附件等非文本内容时是正常的
	// 验证逻辑已调整为允许空的原始消息内容

	if len(t.TargetChannels) == 0 {
		return types.NewValidationError("target_channels", t.TargetChannels, "目标频道列表不能为空")
	}

	if t.SourceChannel == "" {
		return types.NewValidationError("source_channel", t.SourceChannel, "源频道ID不能为空")
	}

	if t.AuthorID == "" {
		return types.NewValidationError("author_id", t.AuthorID, "作者ID不能为空")
	}

	if t.GuildID == "" {
		return types.NewValidationError("guild_id", t.GuildID, "服务器ID不能为空")
	}

	// 验证频道ID格式（Discord雪花ID应该是数字字符串）
	for i, channelID := range t.TargetChannels {
		if len(channelID) < 17 || len(channelID) > 20 {
			return types.NewValidationError(
				fmt.Sprintf("target_channels[%d]", i),
				channelID,
				"无效的频道ID格式")
		}
	}

	if len(t.SourceChannel) < 17 || len(t.SourceChannel) > 20 {
		return types.NewValidationError("source_channel", t.SourceChannel, "无效的源频道ID格式")
	}

	return nil
}

// ParseTaskFromData 从接口数据解析任务
func ParseTaskFromData(data interface{}) (*MessageForwardTask, error) {
	if data == nil {
		return nil, fmt.Errorf("任务数据为空")
	}

	// 记录原始数据用于调试
	logger.Debug("解析任务数据", "data_type", fmt.Sprintf("%T", data), "data", data)

	// 尝试直接类型断言
	if task, ok := data.(*MessageForwardTask); ok {
		return task, nil
	}

	// 尝试从map解析
	if dataMap, ok := data.(map[string]interface{}); ok {
		task, err := parseTaskFromMap(dataMap)
		if err != nil {
			logger.Error("从map解析任务失败", "error", err, "data_map", dataMap)
			return nil, fmt.Errorf("从map解析任务失败: %w", err)
		}
		return task, nil
	}

	// 尝试JSON序列化/反序列化
	jsonData, err := json.Marshal(data)
	if err != nil {
		logger.Error("序列化任务数据失败", "error", err, "data", data)
		return nil, fmt.Errorf("序列化任务数据失败: %w", err)
	}

	var task MessageForwardTask
	if err := json.Unmarshal(jsonData, &task); err != nil {
		logger.Error("反序列化任务数据失败", "error", err, "json_data", string(jsonData))
		return nil, fmt.Errorf("反序列化任务数据失败: %w", err)
	}

	return &task, nil
}

// parseTaskFromMap 从map解析任务
func parseTaskFromMap(dataMap map[string]interface{}) (*MessageForwardTask, error) {
	task := &MessageForwardTask{}

	// 检查是否是嵌套结构（队列任务格式）
	var actualData map[string]interface{}
	if taskData, ok := dataMap["data"].(map[string]interface{}); ok {
		// 这是队列任务格式，实际数据在 data 字段中
		actualData = taskData
		logger.Debug("检测到嵌套任务数据结构", "task_type", dataMap["type"])
	} else {
		// 直接使用原始数据
		actualData = dataMap
	}

	// 解析字符串字段
	if id, ok := actualData["id"].(string); ok {
		task.ID = id
	}
	if originalMessage, ok := actualData["original_message"].(string); ok {
		task.OriginalMessage = originalMessage
	}
	if sourceChannel, ok := actualData["source_channel"].(string); ok {
		task.SourceChannel = sourceChannel
	}
	if authorID, ok := actualData["author_id"].(string); ok {
		task.AuthorID = authorID
	}
	if authorName, ok := actualData["author_name"].(string); ok {
		task.AuthorName = authorName
	}
	if guildID, ok := actualData["guild_id"].(string); ok {
		task.GuildID = guildID
	}
	if messageID, ok := actualData["message_id"].(string); ok {
		task.MessageID = messageID
	}
	if mappingName, ok := actualData["mapping_name"].(string); ok {
		task.MappingName = mappingName
	}

	// 解析目标频道列表
	if targetChannelsRaw, ok := actualData["target_channels"]; ok {
		if targetChannelsList, ok := targetChannelsRaw.([]interface{}); ok {
			task.TargetChannels = make([]string, len(targetChannelsList))
			for i, channel := range targetChannelsList {
				if channelStr, ok := channel.(string); ok {
					task.TargetChannels[i] = channelStr
				}
			}
		}
	}

	// 解析时间字段
	if createdAtRaw, ok := actualData["created_at"]; ok {
		if createdAtStr, ok := createdAtRaw.(string); ok {
			if parsedTime, err := time.Parse(time.RFC3339, createdAtStr); err == nil {
				task.CreatedAt = parsedTime
			}
		} else if createdAtTime, ok := createdAtRaw.(time.Time); ok {
			task.CreatedAt = createdAtTime
		}
	}

	// 忽略 timestamp 字段（已简化）

	// 解析产品信息（新格式，优先使用）
	if productsRaw, ok := actualData["products"]; ok {
		if productsList, ok := productsRaw.([]interface{}); ok {
			task.Products = make([]*types.ProductItem, len(productsList))
			for i, productRaw := range productsList {
				if productMap, ok := productRaw.(map[string]interface{}); ok {
					// 从map创建ProductItem
					product := parseProductItemFromMap(productMap)
					task.Products[i] = product
				}
			}
			logger.Debug("解析产品信息（新格式）", "count", len(task.Products))
		}
	}

	// 忽略向后兼容的 embeds 字段（已简化，只使用 products）

	return task, nil
}

// parseProductItemFromMap 从map解析ProductItem
func parseProductItemFromMap(productMap map[string]interface{}) *types.ProductItem {
	product := &types.ProductItem{
		Metadata:     make(map[string]interface{}),
		Stock:        0,
		Availability: "unknown",
	}

	// 解析基础字段
	if title, ok := productMap["title"].(string); ok {
		product.Title = title
	}
	if url, ok := productMap["url"].(string); ok {
		product.URL = url
	}
	if productID, ok := productMap["productId"].(string); ok {
		product.ProductID = productID
	}
	if price, ok := productMap["price"].(string); ok {
		product.Price = price
	}
	if platform, ok := productMap["platform"].(string); ok {
		product.Platform = platform
	}

	// 解析可选字段
	if description, ok := productMap["description"].(string); ok {
		product.Description = &description
	}
	if color, ok := productMap["color"].(int); ok {
		product.Color = &color
	}
	if imageURL, ok := productMap["image_url"].(string); ok {
		product.ImageURL = &imageURL
	}
	if thumbnailURL, ok := productMap["thumbnail_url"].(string); ok {
		product.ThumbnailURL = &thumbnailURL
	}
	if authorName, ok := productMap["author_name"].(string); ok {
		product.AuthorName = &authorName
	}
	if timestamp, ok := productMap["timestamp"].(string); ok {
		product.Timestamp = &timestamp
	}

	return product
}

// ToMap 将任务转换为map（简化版）
func (t *MessageForwardTask) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"id":               t.ID,
		"original_message": t.OriginalMessage,
		"target_channels":  t.TargetChannels,
		"source_channel":   t.SourceChannel,
		"author_id":        t.AuthorID,
		"author_name":      t.AuthorName,
		"guild_id":         t.GuildID,
		"message_id":       t.MessageID,
		"created_at":       t.CreatedAt,
		"mapping_name":     t.MappingName,
		"products":         t.Products,
	}
}
